/**
 * 物种管理API服务层
 */

import type {
  ApiResponse,
  AudioIdentificationRecord,
  AudioIdentifyRequest,
  GeographicDistribution,
  KMLUploadRequest,
  MediaFile,
  MediaMetadataUpdateRequest,
  MediaUploadRequest,
  PaginatedResponse,
  Species,
  SpeciesCreateRequest,
  SpeciesListQuery,
  SpeciesUpdateRequest,
} from '#/types/species';

// 模拟API延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Mock数据将在后续文件中定义
import {
  mockSpeciesData,
  mockMediaFiles,
  mockIdentificationRecords,
  mockGeographicDistributions
} from './mock-data';

/**
 * 物种管理API
 */
export class SpeciesApi {
  /**
   * 获取物种列表
   */
  static async getSpeciesList(query: SpeciesListQuery = {}): Promise<PaginatedResponse<Species>> {
    await delay(500); // 模拟网络延迟

    const {
      page = 1,
      limit = 10,
      search = '',
      category = '',
      conservationStatus,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = query;

    let filteredData = [...mockSpeciesData];

    // 搜索过滤
    if (search) {
      const searchLower = search.toLowerCase();
      filteredData = filteredData.filter(
        species =>
          species.chineseName.toLowerCase().includes(searchLower) ||
          species.englishName?.toLowerCase().includes(searchLower) ||
          species.latinName.toLowerCase().includes(searchLower)
      );
    }

    // 分类过滤
    if (category) {
      filteredData = filteredData.filter(species =>
        Object.values(species.taxonomy).some(value =>
          value.toLowerCase().includes(category.toLowerCase())
        )
      );
    }

    // 保护状况过滤
    if (conservationStatus) {
      filteredData = filteredData.filter(
        species => species.conservationStatus === conservationStatus
      );
    }

    // 排序
    filteredData.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortBy) {
        case 'name':
          aValue = a.chineseName;
          bValue = b.chineseName;
          break;
        case 'createdAt':
          aValue = new Date(a.createdAt);
          bValue = new Date(b.createdAt);
          break;
        case 'updatedAt':
          aValue = new Date(a.updatedAt);
          bValue = new Date(b.updatedAt);
          break;
        default:
          aValue = a.createdAt;
          bValue = b.createdAt;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    // 分页
    const total = filteredData.length;
    const totalPages = Math.ceil(total / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const data = filteredData.slice(startIndex, endIndex);

    return {
      success: true,
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    };
  }

  /**
   * 获取物种详情
   */
  static async getSpeciesById(id: string): Promise<ApiResponse<Species>> {
    await delay(300);

    const species = mockSpeciesData.find(s => s.id === id);
    
    if (!species) {
      return {
        success: false,
        data: null,
        message: '物种不存在',
        code: 'SPECIES_NOT_FOUND',
      };
    }

    // 添加媒体文件信息
    const mediaFiles = mockMediaFiles.filter(m => m.speciesId === id);
    
    return {
      success: true,
      data: {
        ...species,
        mediaFiles,
      },
    };
  }

  /**
   * 创建物种
   */
  static async createSpecies(data: SpeciesCreateRequest): Promise<ApiResponse<Species>> {
    await delay(800);

    // 检查拉丁学名是否已存在
    const existingSpecies = mockSpeciesData.find(s => s.latinName === data.latinName);
    if (existingSpecies) {
      return {
        success: false,
        data: null,
        message: '拉丁学名已存在',
        code: 'LATIN_NAME_EXISTS',
      };
    }

    const newSpecies: Species = {
      id: `species_${Date.now()}`,
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    mockSpeciesData.unshift(newSpecies);

    return {
      success: true,
      data: newSpecies,
      message: '物种创建成功',
    };
  }

  /**
   * 更新物种
   */
  static async updateSpecies(data: SpeciesUpdateRequest): Promise<ApiResponse<Species>> {
    await delay(600);

    const index = mockSpeciesData.findIndex(s => s.id === data.id);
    if (index === -1) {
      return {
        success: false,
        data: null,
        message: '物种不存在',
        code: 'SPECIES_NOT_FOUND',
      };
    }

    // 检查拉丁学名是否与其他物种冲突
    if (data.latinName) {
      const existingSpecies = mockSpeciesData.find(
        s => s.latinName === data.latinName && s.id !== data.id
      );
      if (existingSpecies) {
        return {
          success: false,
          data: null,
          message: '拉丁学名已存在',
          code: 'LATIN_NAME_EXISTS',
        };
      }
    }

    const updatedSpecies = {
      ...mockSpeciesData[index],
      ...data,
      updatedAt: new Date().toISOString(),
    };

    mockSpeciesData[index] = updatedSpecies;

    return {
      success: true,
      data: updatedSpecies,
      message: '物种更新成功',
    };
  }

  /**
   * 删除物种
   */
  static async deleteSpecies(id: string): Promise<ApiResponse<null>> {
    await delay(400);

    const index = mockSpeciesData.findIndex(s => s.id === id);
    if (index === -1) {
      return {
        success: false,
        data: null,
        message: '物种不存在',
        code: 'SPECIES_NOT_FOUND',
      };
    }

    mockSpeciesData.splice(index, 1);

    // 同时删除相关的媒体文件
    const mediaIndexes = mockMediaFiles
      .map((m, i) => (m.speciesId === id ? i : -1))
      .filter(i => i !== -1)
      .reverse();
    
    mediaIndexes.forEach(i => mockMediaFiles.splice(i, 1));

    return {
      success: true,
      data: null,
      message: '物种删除成功',
    };
  }
}

/**
 * 媒体管理API
 */
export class MediaApi {
  /**
   * 获取物种媒体文件列表
   */
  static async getMediaFiles(speciesId: string, type?: string): Promise<ApiResponse<MediaFile[]>> {
    await delay(300);

    let mediaFiles = mockMediaFiles.filter(m => m.speciesId === speciesId);
    
    if (type) {
      mediaFiles = mediaFiles.filter(m => m.type === type);
    }

    return {
      success: true,
      data: mediaFiles,
    };
  }

  /**
   * 上传媒体文件
   */
  static async uploadMedia(request: MediaUploadRequest): Promise<ApiResponse<MediaFile[]>> {
    await delay(2000); // 模拟文件上传时间

    const { speciesId, type, files } = request;
    const uploadedFiles: MediaFile[] = [];

    for (const file of files) {
      const mediaFile: MediaFile = {
        id: `media_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        speciesId,
        type,
        filename: `${Date.now()}_${file.name}`,
        originalName: file.name,
        url: URL.createObjectURL(file), // 在实际应用中这里应该是服务器返回的URL
        thumbnailUrl: type === 'image' ? URL.createObjectURL(file) : undefined,
        size: file.size,
        mimeType: file.type,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      mockMediaFiles.push(mediaFile);
      uploadedFiles.push(mediaFile);
    }

    return {
      success: true,
      data: uploadedFiles,
      message: `成功上传 ${uploadedFiles.length} 个文件`,
    };
  }

  /**
   * 更新媒体元数据
   */
  static async updateMediaMetadata(request: MediaMetadataUpdateRequest): Promise<ApiResponse<MediaFile>> {
    await delay(400);

    const index = mockMediaFiles.findIndex(m => m.id === request.id);
    if (index === -1) {
      return {
        success: false,
        data: null,
        message: '媒体文件不存在',
        code: 'MEDIA_NOT_FOUND',
      };
    }

    const updatedMedia = {
      ...mockMediaFiles[index],
      title: request.title ?? mockMediaFiles[index].title,
      description: request.description ?? mockMediaFiles[index].description,
      coordinates: request.coordinates ?? mockMediaFiles[index].coordinates,
      metadata: request.metadata 
        ? { ...mockMediaFiles[index].metadata, ...request.metadata }
        : mockMediaFiles[index].metadata,
      updatedAt: new Date().toISOString(),
    };

    mockMediaFiles[index] = updatedMedia;

    return {
      success: true,
      data: updatedMedia,
      message: '媒体信息更新成功',
    };
  }

  /**
   * 删除媒体文件
   */
  static async deleteMedia(id: string): Promise<ApiResponse<null>> {
    await delay(300);

    const index = mockMediaFiles.findIndex(m => m.id === id);
    if (index === -1) {
      return {
        success: false,
        data: null,
        message: '媒体文件不存在',
        code: 'MEDIA_NOT_FOUND',
      };
    }

    mockMediaFiles.splice(index, 1);

    return {
      success: true,
      data: null,
      message: '媒体文件删除成功',
    };
  }
}

/**
 * 地理数据管理API
 */
export class GeographicApi {
  /**
   * 获取物种地理分布数据
   */
  static async getDistribution(speciesId: string): Promise<ApiResponse<GeographicDistribution | null>> {
    await delay(300);

    const distribution = mockGeographicDistributions.find(d => d.speciesId === speciesId);

    return {
      success: true,
      data: distribution || null,
    };
  }

  /**
   * 上传KML文件
   */
  static async uploadKML(request: KMLUploadRequest): Promise<ApiResponse<GeographicDistribution>> {
    await delay(1500); // 模拟文件处理时间

    const { speciesId, file } = request;

    // 模拟KML文件解析
    const mockCoordinates = [
      { latitude: 40.7128, longitude: -74.0060 },
      { latitude: 40.7589, longitude: -73.9851 },
      { latitude: 40.6892, longitude: -74.0445 },
    ];

    const distribution: GeographicDistribution = {
      id: `geo_${Date.now()}`,
      speciesId,
      kmlFileUrl: URL.createObjectURL(file),
      kmlFileName: file.name,
      coordinates: mockCoordinates,
      boundingBox: {
        north: Math.max(...mockCoordinates.map(c => c.latitude)),
        south: Math.min(...mockCoordinates.map(c => c.latitude)),
        east: Math.max(...mockCoordinates.map(c => c.longitude)),
        west: Math.min(...mockCoordinates.map(c => c.longitude)),
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // 更新或添加到mock数据
    const existingIndex = mockGeographicDistributions.findIndex(d => d.speciesId === speciesId);
    if (existingIndex !== -1) {
      mockGeographicDistributions[existingIndex] = distribution;
    } else {
      mockGeographicDistributions.push(distribution);
    }

    return {
      success: true,
      data: distribution,
      message: 'KML文件上传成功',
    };
  }

  /**
   * 删除地理分布数据
   */
  static async deleteDistribution(id: string): Promise<ApiResponse<null>> {
    await delay(300);

    const index = mockGeographicDistributions.findIndex(d => d.id === id);
    if (index === -1) {
      return {
        success: false,
        data: null,
        message: '地理分布数据不存在',
        code: 'DISTRIBUTION_NOT_FOUND',
      };
    }

    mockGeographicDistributions.splice(index, 1);

    return {
      success: true,
      data: null,
      message: '地理分布数据删除成功',
    };
  }
}

/**
 * 音频识别API
 */
export class AudioRecognitionApi {
  /**
   * 音频识别
   */
  static async identifyAudio(request: AudioIdentifyRequest): Promise<ApiResponse<AudioIdentificationRecord>> {
    await delay(3000); // 模拟AI处理时间

    const { audioFile } = request;

    // 模拟识别结果
    const identifiedSpecies = [
      {
        speciesId: 'species_001',
        species: mockSpeciesData.find(s => s.id === 'species_001')!,
        confidence: 0.85 + Math.random() * 0.1,
        probability: 0.78 + Math.random() * 0.15,
      },
    ];

    const record: AudioIdentificationRecord = {
      id: `record_${Date.now()}`,
      audioFileId: `temp_${Date.now()}`,
      audioFile: {
        id: `temp_${Date.now()}`,
        speciesId: '',
        type: 'audio',
        filename: audioFile.name,
        originalName: audioFile.name,
        url: URL.createObjectURL(audioFile),
        size: audioFile.size,
        mimeType: audioFile.type,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      identifiedSpecies,
      processingTime: 2500 + Math.random() * 1000,
      rawResult: {
        model_version: '2.1.0',
        processing_timestamp: new Date().toISOString(),
        audio_features: {
          dominant_frequency: 150 + Math.random() * 2000,
          duration: 10 + Math.random() * 20,
          amplitude_peak: 0.5 + Math.random() * 0.4,
        },
      },
      createdAt: new Date().toISOString(),
    };

    mockIdentificationRecords.unshift(record);

    return {
      success: true,
      data: record,
      message: '音频识别完成',
    };
  }

  /**
   * 获取识别历史记录
   */
  static async getIdentificationHistory(page = 1, limit = 10): Promise<PaginatedResponse<AudioIdentificationRecord>> {
    await delay(400);

    const total = mockIdentificationRecords.length;
    const totalPages = Math.ceil(total / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const data = mockIdentificationRecords.slice(startIndex, endIndex);

    return {
      success: true,
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    };
  }

  /**
   * 删除识别记录
   */
  static async deleteIdentificationRecord(id: string): Promise<ApiResponse<null>> {
    await delay(300);

    const index = mockIdentificationRecords.findIndex(r => r.id === id);
    if (index === -1) {
      return {
        success: false,
        data: null,
        message: '识别记录不存在',
        code: 'RECORD_NOT_FOUND',
      };
    }

    mockIdentificationRecords.splice(index, 1);

    return {
      success: true,
      data: null,
      message: '识别记录删除成功',
    };
  }
}
