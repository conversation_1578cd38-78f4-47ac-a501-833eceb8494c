<template>
  <div class="rich-text-editor">
    <div class="mb-2 flex items-center justify-between">
      <label v-if="label" class="text-sm font-medium text-gray-700">
        {{ label }}
      </label>
      <div class="flex space-x-2">
        <a-button
          v-if="showPreview"
          size="small"
          :type="previewMode ? 'primary' : 'default'"
          @click="togglePreview"
        >
          <template #icon>
            <EyeOutlined v-if="!previewMode" />
            <EditOutlined v-else />
          </template>
          {{ previewMode ? '编辑' : '预览' }}
        </a-button>
        <a-button v-if="showFullscreen" size="small" @click="toggleFullscreen">
          <template #icon>
            <FullscreenOutlined v-if="!isFullscreen" />
            <FullscreenExitOutlined v-else />
          </template>
          {{ isFullscreen ? '退出全屏' : '全屏' }}
        </a-button>
      </div>
    </div>

    <div
      ref="editorContainer"
      :class="[
        'rich-text-editor-container',
        {
          'fullscreen': isFullscreen,
          'preview-mode': previewMode,
        }
      ]"
    >
      <!-- 编辑器 -->
      <div v-show="!previewMode" class="editor-wrapper">
        <QuillEditor
          ref="quillEditor"
          v-model:content="content"
          :options="editorOptions"
          :placeholder="placeholder"
          content-type="html"
          @update:content="handleContentChange"
          @ready="handleEditorReady"
        />
      </div>

      <!-- 预览 -->
      <div v-show="previewMode" class="preview-wrapper">
        <div class="preview-content" v-html="content"></div>
      </div>
    </div>

    <!-- 字数统计 -->
    <div v-if="showWordCount" class="mt-2 text-right text-xs text-gray-500">
      字数: {{ wordCount }} / {{ maxLength || '无限制' }}
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="mt-1 text-sm text-red-600">
      {{ error }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, ref, watch } from 'vue';
import { QuillEditor } from '@vueup/vue-quill';
import '@vueup/vue-quill/dist/vue-quill.snow.css';
import {
  EditOutlined,
  EyeOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
} from '@ant-design/icons-vue';

interface Props {
  modelValue?: string;
  label?: string;
  placeholder?: string;
  readonly?: boolean;
  maxLength?: number;
  showPreview?: boolean;
  showFullscreen?: boolean;
  showWordCount?: boolean;
  height?: string | number;
  error?: string;
}

interface Emits {
  (e: 'update:modelValue', value: string): void;
  (e: 'change', value: string): void;
  (e: 'blur'): void;
  (e: 'focus'): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请输入内容...',
  readonly: false,
  showPreview: true,
  showFullscreen: true,
  showWordCount: true,
  height: 300,
});

const emit = defineEmits<Emits>();

// 响应式数据
const quillEditor = ref();
const editorContainer = ref<HTMLElement>();
const content = ref(props.modelValue);
const previewMode = ref(false);
const isFullscreen = ref(false);

// 编辑器配置
const editorOptions = computed(() => ({
  theme: 'snow',
  readOnly: props.readonly,
  modules: {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ header: 1 }, { header: 2 }],
      [{ list: 'ordered' }, { list: 'bullet' }],
      [{ script: 'sub' }, { script: 'super' }],
      [{ indent: '-1' }, { indent: '+1' }],
      [{ direction: 'rtl' }],
      [{ size: ['small', false, 'large', 'huge'] }],
      [{ header: [1, 2, 3, 4, 5, 6, false] }],
      [{ color: [] }, { background: [] }],
      [{ font: [] }],
      [{ align: [] }],
      ['clean'],
      ['link', 'image'],
    ],
  },
  placeholder: props.placeholder,
}));

// 字数统计
const wordCount = computed(() => {
  if (!content.value) return 0;
  // 移除HTML标签后计算字数
  const textContent = content.value.replace(/<[^>]*>/g, '');
  return textContent.length;
});

// 监听外部值变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue !== content.value) {
      content.value = newValue || '';
    }
  },
  { immediate: true }
);

// 监听内容变化
watch(content, (newValue) => {
  emit('update:modelValue', newValue);
  emit('change', newValue);
});

// 处理内容变化
const handleContentChange = (value: string) => {
  content.value = value;
};

// 编辑器准备就绪
const handleEditorReady = (quill: any) => {
  // 设置编辑器高度
  const editorElement = quill.container.querySelector('.ql-editor');
  if (editorElement) {
    editorElement.style.minHeight = typeof props.height === 'number' 
      ? `${props.height}px` 
      : props.height;
  }

  // 绑定事件
  quill.on('selection-change', (range: any) => {
    if (range) {
      emit('focus');
    } else {
      emit('blur');
    }
  });
};

// 切换预览模式
const togglePreview = () => {
  previewMode.value = !previewMode.value;
};

// 切换全屏模式
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
  
  if (isFullscreen.value) {
    document.body.style.overflow = 'hidden';
    nextTick(() => {
      editorContainer.value?.focus();
    });
  } else {
    document.body.style.overflow = '';
  }
};

// 监听ESC键退出全屏
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && isFullscreen.value) {
    toggleFullscreen();
  }
};

// 组件挂载时添加键盘监听
import { onMounted, onUnmounted } from 'vue';

onMounted(() => {
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
  if (isFullscreen.value) {
    document.body.style.overflow = '';
  }
});

// 暴露方法给父组件
defineExpose({
  focus: () => quillEditor.value?.focus(),
  blur: () => quillEditor.value?.blur(),
  getQuill: () => quillEditor.value?.getQuill(),
  getHTML: () => content.value,
  getText: () => quillEditor.value?.getText(),
  getLength: () => quillEditor.value?.getLength(),
  insertText: (index: number, text: string) => quillEditor.value?.insertText(index, text),
  insertEmbed: (index: number, type: string, value: any) => quillEditor.value?.insertEmbed(index, type, value),
});
</script>

<style scoped>
.rich-text-editor {
  @apply w-full;
}

.rich-text-editor-container {
  @apply relative border border-gray-300 rounded-md;
  transition: all 0.3s ease;
}

.rich-text-editor-container.fullscreen {
  @apply fixed inset-0 z-50 bg-white;
  border-radius: 0;
}

.editor-wrapper {
  @apply relative;
}

.preview-wrapper {
  @apply p-4 min-h-[300px] bg-gray-50;
}

.preview-content {
  @apply prose prose-sm max-w-none;
}

.preview-content :deep(h1) {
  @apply text-2xl font-bold mb-4;
}

.preview-content :deep(h2) {
  @apply text-xl font-bold mb-3;
}

.preview-content :deep(h3) {
  @apply text-lg font-bold mb-2;
}

.preview-content :deep(p) {
  @apply mb-3 leading-relaxed;
}

.preview-content :deep(ul),
.preview-content :deep(ol) {
  @apply mb-3 pl-6;
}

.preview-content :deep(li) {
  @apply mb-1;
}

.preview-content :deep(blockquote) {
  @apply border-l-4 border-gray-300 pl-4 italic text-gray-600 mb-3;
}

.preview-content :deep(code) {
  @apply bg-gray-100 px-1 py-0.5 rounded text-sm font-mono;
}

.preview-content :deep(pre) {
  @apply bg-gray-100 p-3 rounded mb-3 overflow-x-auto;
}

.preview-content :deep(img) {
  @apply max-w-full h-auto rounded;
}

.preview-content :deep(table) {
  @apply w-full border-collapse border border-gray-300 mb-3;
}

.preview-content :deep(th),
.preview-content :deep(td) {
  @apply border border-gray-300 px-3 py-2;
}

.preview-content :deep(th) {
  @apply bg-gray-100 font-bold;
}

/* Quill编辑器样式覆盖 */
:deep(.ql-editor) {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
}

:deep(.ql-toolbar) {
  border-top: none;
  border-left: none;
  border-right: none;
  border-bottom: 1px solid #e5e7eb;
}

:deep(.ql-container) {
  border: none;
  font-size: 14px;
}

.fullscreen :deep(.ql-editor) {
  min-height: calc(100vh - 120px) !important;
}
</style>
