# 设计文档

## 概述

物种详情管理系统是一个基于现代Web技术栈的后台管理平台，为管理员提供全面的物种数据管理功能。系统采用前后端分离架构，支持大文件处理、地理空间数据管理、AI音频识别等复杂功能。

## 架构设计

### 整体架构

```mermaid
graph TB
    A[前端 - Vben Admin] --> B[API网关]
    B --> C[业务服务层]
    C --> D[数据访问层]
    D --> E[PostgreSQL数据库]
    D --> F[Redis缓存]
    C --> G[文件存储服务]
    C --> H[第三方服务]
    
    subgraph "业务服务层"
        C1[物种管理服务]
        C2[媒体管理服务]
        C3[地理数据服务]
        C4[音频识别服务]
    end
    
    subgraph "第三方服务"
        H1[音频识别API]
        H2[地图服务API]
        H3[云存储OSS]
    end
```

### 技术栈选择

**前端技术栈：**
- Vue 3 + TypeScript + Vite
- Ant Design Vue (基于现有Vben Admin框架)
- Vue Router 4 (路由管理)
- Pinia (状态管理)
- Axios (HTTP客户端)

**后端技术栈：**
- Node.js + Express + TypeScript
- Prisma ORM (数据库操作)
- Multer (文件上传处理)
- Sharp (图像处理)
- FFmpeg (音频/视频处理)

**数据存储：**
- PostgreSQL (主数据库，支持PostGIS地理扩展)
- Redis (缓存和会话存储)
- 云存储OSS (媒体文件存储)

## 组件和接口设计

### 前端组件架构

```mermaid
graph TD
    A[App.vue] --> B[Layout组件]
    B --> C[路由视图]
    
    subgraph "页面组件"
        C --> D[物种列表页]
        C --> E[物种详情编辑页]
        C --> F[媒体管理页]
        C --> G[音频识别页]
    end
    
    subgraph "通用组件"
        H[富文本编辑器]
        I[文件上传组件]
        J[地图组件]
        K[音频播放器]
        L[图片预览组件]
    end
    
    subgraph "业务组件"
        M[物种信息表单]
        N[分类选择器]
        O[媒体库管理]
        P[坐标输入器]
    end
```

### 核心页面组件

#### 1. 物种管理主页面 (SpeciesManagement.vue)
```typescript
interface SpeciesManagementProps {
  // 物种列表数据
  speciesList: Species[]
  // 分页信息
  pagination: PaginationConfig
  // 搜索过滤条件
  filters: SpeciesFilters
}
```

#### 2. 物种详情编辑页面 (SpeciesEditor.vue)
```typescript
interface SpeciesEditorProps {
  speciesId?: string
  mode: 'create' | 'edit'
}

// 包含子组件：
// - BasicInfoForm (基础信息表单)
// - RichTextEditor (详细介绍编辑器)
// - MediaLibrary (媒体资源库)
// - GeographicDataManager (地理数据管理)
```

#### 3. 媒体管理页面 (MediaManager.vue)
```typescript
interface MediaManagerProps {
  speciesId: string
  mediaType: 'image' | 'video' | 'audio'
}

// 包含子组件：
// - FileUploader (文件上传器)
// - MediaGrid (媒体网格展示)
// - AudioMetadataEditor (音频元数据编辑器)
// - SpectrogramGenerator (频谱图生成器)
```

### 后端API接口设计

#### 物种管理API

```typescript
// GET /api/species - 获取物种列表
interface GetSpeciesListRequest {
  page?: number
  limit?: number
  search?: string
  category?: string
  conservationStatus?: string
}

interface GetSpeciesListResponse {
  data: Species[]
  total: number
  page: number
  limit: number
}

// POST /api/species - 创建物种
interface CreateSpeciesRequest {
  basicInfo: SpeciesBasicInfo
  description?: string
  mediaIds?: string[]
}

// PUT /api/species/:id - 更新物种
interface UpdateSpeciesRequest {
  basicInfo?: Partial<SpeciesBasicInfo>
  description?: string
  representativeImageId?: string
}
```

#### 媒体管理API

```typescript
// POST /api/media/upload - 上传媒体文件
interface UploadMediaRequest {
  files: File[]
  speciesId: string
  mediaType: 'image' | 'video' | 'audio'
}

// PUT /api/media/:id/metadata - 更新媒体元数据
interface UpdateMediaMetadataRequest {
  title?: string
  description?: string
  location?: GeoCoordinate
  recordingInfo?: AudioRecordingInfo
}

// POST /api/media/audio/spectrogram - 生成频谱图
interface GenerateSpectrogramRequest {
  audioId: string
  type: 'hologram' | 'spectrum'
}
```

#### 地理数据API

```typescript
// POST /api/geographic/kml - 上传KML文件
interface UploadKMLRequest {
  file: File
  speciesId: string
}

// GET /api/geographic/distribution/:speciesId - 获取分布数据
interface GetDistributionResponse {
  kmlData: string
  coordinates: GeoCoordinate[]
  boundingBox: BoundingBox
}
```

#### 音频识别API

```typescript
// POST /api/audio/identify - 音频识别
interface AudioIdentifyRequest {
  audioFile: File
}

interface AudioIdentifyResponse {
  species: IdentifiedSpecies[]
  confidence: number
  processingTime: number
  audioMetadata: AudioMetadata
}

// GET /api/audio/identify/history - 获取识别历史
interface GetIdentifyHistoryResponse {
  records: IdentificationRecord[]
  total: number
}
```

## 数据模型设计

### 核心数据模型

```typescript
// 物种基础信息
interface Species {
  id: string
  chineseName: string
  englishName: string
  latinName: string
  taxonomy: SpeciesTaxonomy
  conservationStatus: ConservationStatus
  description?: string
  representativeImageId?: string
  createdAt: Date
  updatedAt: Date
}

// 物种分类信息
interface SpeciesTaxonomy {
  kingdom: string    // 界
  phylum: string     // 门
  class: string      // 纲
  order: string      // 目
  family: string     // 科
  genus: string      // 属
  species: string    // 种
}

// 媒体文件
interface MediaFile {
  id: string
  speciesId: string
  type: 'image' | 'video' | 'audio'
  filename: string
  originalName: string
  url: string
  size: number
  mimeType: string
  metadata?: MediaMetadata
  coordinates?: GeoCoordinate
  createdAt: Date
}

// 音频元数据
interface AudioMetadata {
  duration: number
  sampleRate: number
  bitDepth: number
  channels: number
  recordingLocation?: string
  recordingTime?: Date
  behaviorDescription?: string
  sensitivity?: string
  amplification?: string
  spectrogramUrls?: {
    hologram?: string
    spectrum?: string
  }
}

// 地理坐标
interface GeoCoordinate {
  latitude: number
  longitude: number
  altitude?: number
}

// 地理分布数据
interface GeographicDistribution {
  id: string
  speciesId: string
  kmlFileUrl: string
  coordinates: GeoCoordinate[]
  boundingBox: BoundingBox
  createdAt: Date
}
```

### 数据库表结构

```sql
-- 物种表
CREATE TABLE species (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  chinese_name VARCHAR(255) NOT NULL,
  english_name VARCHAR(255),
  latin_name VARCHAR(255) NOT NULL,
  kingdom VARCHAR(100),
  phylum VARCHAR(100),
  class VARCHAR(100),
  "order" VARCHAR(100),
  family VARCHAR(100),
  genus VARCHAR(100),
  species_name VARCHAR(100),
  conservation_status VARCHAR(50),
  description TEXT,
  representative_image_id UUID,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 媒体文件表
CREATE TABLE media_files (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  species_id UUID REFERENCES species(id) ON DELETE CASCADE,
  type VARCHAR(20) NOT NULL CHECK (type IN ('image', 'video', 'audio')),
  filename VARCHAR(255) NOT NULL,
  original_name VARCHAR(255) NOT NULL,
  url VARCHAR(500) NOT NULL,
  size BIGINT NOT NULL,
  mime_type VARCHAR(100) NOT NULL,
  metadata JSONB,
  coordinates POINT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 地理分布表
CREATE TABLE geographic_distributions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  species_id UUID REFERENCES species(id) ON DELETE CASCADE,
  kml_file_url VARCHAR(500) NOT NULL,
  coordinates POLYGON,
  bounding_box BOX,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 音频识别记录表
CREATE TABLE audio_identifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  audio_file_id UUID REFERENCES media_files(id),
  identified_species_id UUID REFERENCES species(id),
  confidence DECIMAL(5,4),
  processing_time INTEGER,
  raw_result JSONB,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 错误处理设计

### 前端错误处理

```typescript
// 全局错误处理器
class ErrorHandler {
  static handleApiError(error: AxiosError) {
    const { response } = error
    
    switch (response?.status) {
      case 400:
        message.error('请求参数错误')
        break
      case 401:
        message.error('未授权访问')
        router.push('/login')
        break
      case 403:
        message.error('权限不足')
        break
      case 413:
        message.error('文件过大，请选择较小的文件')
        break
      case 422:
        message.error('数据验证失败')
        break
      case 500:
        message.error('服务器内部错误')
        break
      default:
        message.error('网络错误，请稍后重试')
    }
  }
  
  static handleFileUploadError(error: FileUploadError) {
    switch (error.type) {
      case 'FILE_TOO_LARGE':
        message.error(`文件大小超过限制 (${error.maxSize}MB)`)
        break
      case 'INVALID_FILE_TYPE':
        message.error(`不支持的文件类型: ${error.fileType}`)
        break
      case 'UPLOAD_FAILED':
        message.error('文件上传失败，请重试')
        break
    }
  }
}
```

### 后端错误处理

```typescript
// 自定义错误类
class AppError extends Error {
  constructor(
    public statusCode: number,
    public message: string,
    public code?: string
  ) {
    super(message)
  }
}

// 全局错误处理中间件
const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  if (error instanceof AppError) {
    return res.status(error.statusCode).json({
      success: false,
      message: error.message,
      code: error.code
    })
  }
  
  // 处理文件上传错误
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(413).json({
        success: false,
        message: '文件大小超过限制'
      })
    }
  }
  
  // 处理数据库错误
  if (error.name === 'PrismaClientKnownRequestError') {
    return res.status(400).json({
      success: false,
      message: '数据操作失败'
    })
  }
  
  // 默认服务器错误
  res.status(500).json({
    success: false,
    message: '服务器内部错误'
  })
}
```

## 测试策略

### 前端测试

```typescript
// 组件单元测试示例
describe('SpeciesEditor', () => {
  test('should render basic info form', () => {
    const wrapper = mount(SpeciesEditor, {
      props: { mode: 'create' }
    })
    expect(wrapper.find('[data-testid="basic-info-form"]').exists()).toBe(true)
  })
  
  test('should validate required fields', async () => {
    const wrapper = mount(SpeciesEditor)
    await wrapper.find('[data-testid="submit-btn"]').trigger('click')
    expect(wrapper.find('.error-message').text()).toContain('请填写物种中文名')
  })
})

// API集成测试
describe('Species API', () => {
  test('should create species successfully', async () => {
    const speciesData = {
      chineseName: '测试物种',
      latinName: 'Test species'
    }
    
    const response = await api.post('/species', speciesData)
    expect(response.status).toBe(201)
    expect(response.data.chineseName).toBe('测试物种')
  })
})
```

### 后端测试

```typescript
// 服务层单元测试
describe('SpeciesService', () => {
  test('should create species with valid data', async () => {
    const speciesData = {
      chineseName: '测试物种',
      latinName: 'Test species'
    }
    
    const result = await speciesService.create(speciesData)
    expect(result.id).toBeDefined()
    expect(result.chineseName).toBe('测试物种')
  })
  
  test('should throw error for duplicate latin name', async () => {
    await expect(
      speciesService.create({ latinName: 'Existing species' })
    ).rejects.toThrow('物种拉丁名已存在')
  })
})

// API端点测试
describe('POST /api/species', () => {
  test('should return 201 for valid species data', async () => {
    const response = await request(app)
      .post('/api/species')
      .send({
        chineseName: '测试物种',
        latinName: 'Test species'
      })
      .expect(201)
    
    expect(response.body.data.chineseName).toBe('测试物种')
  })
})
```

### 端到端测试

```typescript
// E2E测试场景
describe('Species Management E2E', () => {
  test('complete species creation workflow', async () => {
    // 1. 登录系统
    await page.goto('/login')
    await page.fill('[data-testid="username"]', 'admin')
    await page.fill('[data-testid="password"]', 'password')
    await page.click('[data-testid="login-btn"]')
    
    // 2. 创建新物种
    await page.goto('/species/create')
    await page.fill('[data-testid="chinese-name"]', '测试物种')
    await page.fill('[data-testid="latin-name"]', 'Test species')
    
    // 3. 上传代表性图片
    await page.setInputFiles('[data-testid="image-upload"]', 'test-image.jpg')
    
    // 4. 保存物种
    await page.click('[data-testid="save-btn"]')
    
    // 5. 验证创建成功
    await expect(page.locator('.success-message')).toContainText('物种创建成功')
  })
})
```
