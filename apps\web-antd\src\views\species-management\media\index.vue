<template>
  <div class="media-management-container">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">媒体管理</h1>
      <p class="mt-1 text-sm text-gray-600">
        管理物种的图片、视频和音频资源
      </p>
    </div>

    <!-- 媒体类型切换 -->
    <div class="mb-6">
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
        <a-tab-pane key="image" tab="图片管理">
          <template #tab>
            <FileImageOutlined />
            图片管理 ({{ imageCount }})
          </template>
        </a-tab-pane>
        <a-tab-pane key="video" tab="视频管理">
          <template #tab>
            <VideoCameraOutlined />
            视频管理 ({{ videoCount }})
          </template>
        </a-tab-pane>
        <a-tab-pane key="audio" tab="音频管理">
          <template #tab>
            <AudioOutlined />
            音频管理 ({{ audioCount }})
          </template>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 上传区域 -->
    <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
      <a-upload-dragger
        v-model:file-list="fileList"
        name="files"
        :multiple="true"
        :before-upload="beforeUpload"
        :custom-request="handleUpload"
        :accept="getAcceptTypes()"
      >
        <p class="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p class="ant-upload-text">
          点击或拖拽文件到此区域上传
        </p>
        <p class="ant-upload-hint">
          支持{{ getFileTypeHint() }}格式，支持批量上传
        </p>
      </a-upload-dragger>
    </div>

    <!-- 媒体文件列表 -->
    <div class="rounded-lg bg-white shadow-sm">
      <div class="border-b border-gray-200 p-4">
        <h3 class="text-lg font-medium text-gray-900">
          {{ getTabTitle() }}
        </h3>
      </div>

      <div v-if="loading" class="p-8 text-center">
        <a-spin size="large" />
      </div>

      <div v-else-if="mediaFiles.length === 0" class="p-8 text-center text-gray-500">
        <FileImageOutlined class="mb-2 text-4xl" />
        <p>暂无{{ getTabTitle() }}文件</p>
      </div>

      <div v-else class="p-4">
        <!-- 图片网格 -->
        <div v-if="activeTab === 'image'" class="grid grid-cols-4 gap-4">
          <div
            v-for="file in mediaFiles"
            :key="file.id"
            class="group relative overflow-hidden rounded-lg border border-gray-200"
          >
            <img
              :src="file.thumbnailUrl || file.url"
              :alt="file.title || file.originalName"
              class="h-48 w-full object-cover"
            />
            <div class="absolute inset-0 bg-black bg-opacity-0 transition-all group-hover:bg-opacity-50">
              <div class="absolute bottom-2 left-2 right-2 opacity-0 transition-opacity group-hover:opacity-100">
                <p class="truncate text-sm text-white">{{ file.title || file.originalName }}</p>
              </div>
              <div class="absolute right-2 top-2 opacity-0 transition-opacity group-hover:opacity-100">
                <a-space>
                  <a-button size="small" type="primary" ghost @click="handlePreview(file)">
                    <EyeOutlined />
                  </a-button>
                  <a-button size="small" type="primary" ghost @click="handleEdit(file)">
                    <EditOutlined />
                  </a-button>
                  <a-popconfirm
                    title="确定要删除这个文件吗？"
                    @confirm="handleDelete(file)"
                  >
                    <a-button size="small" danger ghost>
                      <DeleteOutlined />
                    </a-button>
                  </a-popconfirm>
                </a-space>
              </div>
            </div>
          </div>
        </div>

        <!-- 视频/音频列表 -->
        <div v-else>
          <a-list
            :data-source="mediaFiles"
            item-layout="horizontal"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <template #actions>
                  <a-button type="link" @click="handlePreview(item)">
                    <EyeOutlined /> 预览
                  </a-button>
                  <a-button type="link" @click="handleEdit(item)">
                    <EditOutlined /> 编辑
                  </a-button>
                  <a-popconfirm
                    title="确定要删除这个文件吗？"
                    @confirm="handleDelete(item)"
                  >
                    <a-button type="link" danger>
                      <DeleteOutlined /> 删除
                    </a-button>
                  </a-popconfirm>
                </template>

                <a-list-item-meta>
                  <template #avatar>
                    <a-avatar shape="square" :size="64">
                      <template #icon>
                        <VideoCameraOutlined v-if="activeTab === 'video'" />
                        <AudioOutlined v-else />
                      </template>
                    </a-avatar>
                  </template>
                  <template #title>
                    <div>{{ item.title || item.originalName }}</div>
                  </template>
                  <template #description>
                    <div class="text-sm text-gray-500">
                      <p>文件大小: {{ formatFileSize(item.size) }}</p>
                      <p>上传时间: {{ new Date(item.createdAt).toLocaleString() }}</p>
                      <p v-if="item.description">描述: {{ item.description }}</p>
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </div>
    </div>

    <!-- 预览模态框 -->
    <a-modal
      v-model:open="previewVisible"
      :title="previewFile?.title || previewFile?.originalName"
      :footer="null"
      width="80%"
      centered
    >
      <div v-if="previewFile" class="text-center">
        <img
          v-if="previewFile.type === 'image'"
          :src="previewFile.url"
          :alt="previewFile.title || previewFile.originalName"
          class="max-h-96 max-w-full"
        />
        <video
          v-else-if="previewFile.type === 'video'"
          :src="previewFile.url"
          controls
          class="max-h-96 max-w-full"
        />
        <audio
          v-else-if="previewFile.type === 'audio'"
          :src="previewFile.url"
          controls
          class="w-full"
        />
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import {
  AudioOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  FileImageOutlined,
  InboxOutlined,
  VideoCameraOutlined,
} from '@ant-design/icons-vue';

import type { UploadFile, UploadProps } from 'ant-design-vue';
import type { MediaFile, MediaType } from '#/types/species';
import { MediaApi } from '#/api/species';

// 路由
const route = useRoute();
const speciesId = computed(() => route.params.id as string);

// 响应式数据
const loading = ref(false);
const activeTab = ref<MediaType>('image');
const mediaFiles = ref<MediaFile[]>([]);
const fileList = ref<UploadFile[]>([]);
const previewVisible = ref(false);
const previewFile = ref<MediaFile | null>(null);

// 计算属性
const imageCount = computed(() => mediaFiles.value.filter(f => f.type === 'image').length);
const videoCount = computed(() => mediaFiles.value.filter(f => f.type === 'video').length);
const audioCount = computed(() => mediaFiles.value.filter(f => f.type === 'audio').length);

// 获取接受的文件类型
const getAcceptTypes = (): string => {
  const typeMap = {
    image: 'image/*',
    video: 'video/*',
    audio: 'audio/*',
  };
  return typeMap[activeTab.value];
};

// 获取文件类型提示
const getFileTypeHint = (): string => {
  const hintMap = {
    image: '图片',
    video: '视频',
    audio: '音频',
  };
  return hintMap[activeTab.value];
};

// 获取标签标题
const getTabTitle = (): string => {
  const titleMap = {
    image: '图片',
    video: '视频',
    audio: '音频',
  };
  return titleMap[activeTab.value];
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 加载媒体文件
const loadMediaFiles = async () => {
  try {
    loading.value = true;
    const response = await MediaApi.getMediaFiles(speciesId.value, activeTab.value);
    
    if (response.success) {
      mediaFiles.value = response.data || [];
    } else {
      message.error(response.message || '加载媒体文件失败');
    }
  } catch (error) {
    console.error('加载媒体文件失败:', error);
    message.error('加载媒体文件失败');
  } finally {
    loading.value = false;
  }
};

// 切换标签
const handleTabChange = (key: string) => {
  activeTab.value = key as MediaType;
  fileList.value = [];
  loadMediaFiles();
};

// 上传前检查
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const isValidType = file.type?.startsWith(activeTab.value);
  if (!isValidType) {
    message.error(`只能上传${getFileTypeHint()}文件！`);
    return false;
  }

  const isLt100M = file.size! / 1024 / 1024 < 100;
  if (!isLt100M) {
    message.error('文件大小不能超过100MB！');
    return false;
  }

  return false; // 阻止自动上传，使用自定义上传
};

// 自定义上传
const handleUpload: UploadProps['customRequest'] = async (options) => {
  const { file, onSuccess, onError } = options;
  
  try {
    const response = await MediaApi.uploadMedia({
      speciesId: speciesId.value,
      type: activeTab.value,
      files: [file as File],
    });

    if (response.success) {
      message.success('文件上传成功');
      onSuccess?.(response.data);
      loadMediaFiles();
      fileList.value = [];
    } else {
      message.error(response.message || '上传失败');
      onError?.(new Error(response.message || '上传失败'));
    }
  } catch (error) {
    console.error('上传失败:', error);
    message.error('上传失败');
    onError?.(error as Error);
  }
};

// 预览文件
const handlePreview = (file: MediaFile) => {
  previewFile.value = file;
  previewVisible.value = true;
};

// 编辑文件
const handleEdit = (file: MediaFile) => {
  // TODO: 实现编辑功能
  message.info('编辑功能开发中...');
};

// 删除文件
const handleDelete = async (file: MediaFile) => {
  try {
    const response = await MediaApi.deleteMedia(file.id);
    if (response.success) {
      message.success('文件删除成功');
      loadMediaFiles();
    } else {
      message.error(response.message || '删除失败');
    }
  } catch (error) {
    console.error('删除文件失败:', error);
    message.error('删除文件失败');
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadMediaFiles();
});
</script>

<style scoped>
.media-management-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}
</style>
