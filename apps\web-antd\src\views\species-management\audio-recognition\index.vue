<template>
  <div class="audio-recognition-container">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">音频识别</h1>
      <p class="mt-1 text-sm text-gray-600">
        上传音频文件进行物种识别，查看识别历史记录
      </p>
    </div>

    <a-row :gutter="24">
      <!-- 左侧：音频上传和识别 -->
      <a-col :span="12">
        <div class="rounded-lg bg-white p-6 shadow-sm">
          <h3 class="mb-4 text-lg font-medium text-gray-900">音频识别</h3>
          
          <!-- 音频上传 -->
          <a-upload-dragger
            v-model:file-list="audioFileList"
            name="audioFile"
            :multiple="false"
            :before-upload="beforeUpload"
            :custom-request="handleUpload"
            accept="audio/*"
            :disabled="recognizing"
          >
            <p class="ant-upload-drag-icon">
              <AudioOutlined />
            </p>
            <p class="ant-upload-text">
              点击或拖拽音频文件到此区域
            </p>
            <p class="ant-upload-hint">
              支持WAV、MP3格式，文件大小不超过50MB
            </p>
          </a-upload-dragger>

          <!-- 识别进度 -->
          <div v-if="recognizing" class="mt-6">
            <a-progress
              :percent="recognitionProgress"
              :status="recognitionStatus"
              :show-info="true"
            />
            <p class="mt-2 text-center text-sm text-gray-600">
              {{ recognitionMessage }}
            </p>
          </div>

          <!-- 识别结果 -->
          <div v-if="currentResult" class="mt-6">
            <h4 class="mb-3 text-base font-medium text-gray-900">识别结果</h4>
            <div class="rounded-lg border border-gray-200 p-4">
              <div v-if="currentResult.identifiedSpecies.length > 0">
                <div
                  v-for="species in currentResult.identifiedSpecies"
                  :key="species.speciesId"
                  class="mb-4 last:mb-0"
                >
                  <div class="flex items-center justify-between">
                    <div>
                      <h5 class="font-medium text-gray-900">
                        {{ species.species.chineseName }}
                      </h5>
                      <p class="text-sm text-gray-500">
                        {{ species.species.englishName }}
                      </p>
                      <p class="text-xs italic text-gray-400">
                        {{ species.species.latinName }}
                      </p>
                    </div>
                    <div class="text-right">
                      <div class="text-lg font-semibold text-green-600">
                        {{ (species.confidence * 100).toFixed(1) }}%
                      </div>
                      <div class="text-sm text-gray-500">置信度</div>
                    </div>
                  </div>
                  <a-progress
                    :percent="species.confidence * 100"
                    :show-info="false"
                    size="small"
                    stroke-color="#10b981"
                  />
                </div>
              </div>
              <div v-else class="text-center text-gray-500">
                <AudioOutlined class="mb-2 text-2xl" />
                <p>未识别到匹配的物种</p>
              </div>

              <div class="mt-4 border-t border-gray-200 pt-4">
                <div class="flex justify-between text-sm text-gray-600">
                  <span>处理时间: {{ currentResult.processingTime }}ms</span>
                  <span>识别时间: {{ new Date(currentResult.createdAt).toLocaleString() }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </a-col>

      <!-- 右侧：识别历史 -->
      <a-col :span="12">
        <div class="rounded-lg bg-white p-6 shadow-sm">
          <div class="mb-4 flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">识别历史</h3>
            <a-button size="small" @click="loadHistory">
              <template #icon>
                <ReloadOutlined />
              </template>
              刷新
            </a-button>
          </div>

          <div v-if="historyLoading" class="py-8 text-center">
            <a-spin size="large" />
          </div>

          <div v-else-if="historyRecords.length === 0" class="py-8 text-center text-gray-500">
            <HistoryOutlined class="mb-2 text-4xl" />
            <p>暂无识别记录</p>
          </div>

          <div v-else class="max-h-96 overflow-y-auto">
            <a-list
              :data-source="historyRecords"
              size="small"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <template #actions>
                    <a-button type="link" size="small" @click="handlePlayAudio(item)">
                      <PlayCircleOutlined /> 播放
                    </a-button>
                    <a-popconfirm
                      title="确定要删除这条记录吗？"
                      @confirm="handleDeleteRecord(item)"
                    >
                      <a-button type="link" size="small" danger>
                        <DeleteOutlined /> 删除
                      </a-button>
                    </a-popconfirm>
                  </template>

                  <a-list-item-meta>
                    <template #avatar>
                      <a-avatar>
                        <template #icon>
                          <AudioOutlined />
                        </template>
                      </a-avatar>
                    </template>
                    <template #title>
                      <div class="text-sm">
                        {{ item.audioFile.originalName }}
                      </div>
                    </template>
                    <template #description>
                      <div class="text-xs text-gray-500">
                        <div v-if="item.identifiedSpecies.length > 0">
                          识别为: {{ item.identifiedSpecies[0].species.chineseName }}
                          ({{ (item.identifiedSpecies[0].confidence * 100).toFixed(1) }}%)
                        </div>
                        <div v-else>未识别到匹配物种</div>
                        <div>{{ new Date(item.createdAt).toLocaleString() }}</div>
                      </div>
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>

            <!-- 分页 -->
            <div v-if="historyTotal > historyRecords.length" class="mt-4 text-center">
              <a-button @click="loadMoreHistory">
                加载更多
              </a-button>
            </div>
          </div>
        </div>
      </a-col>
    </a-row>

    <!-- 音频播放模态框 -->
    <a-modal
      v-model:open="audioPlayerVisible"
      :title="playingAudio?.audioFile.originalName"
      :footer="null"
      width="500px"
      centered
    >
      <div v-if="playingAudio" class="text-center">
        <audio
          :src="playingAudio.audioFile.url"
          controls
          class="w-full"
          autoplay
        />
        <div class="mt-4 text-sm text-gray-600">
          <p>文件大小: {{ formatFileSize(playingAudio.audioFile.size) }}</p>
          <p>识别时间: {{ new Date(playingAudio.createdAt).toLocaleString() }}</p>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { message } from 'ant-design-vue';
import {
  AudioOutlined,
  DeleteOutlined,
  HistoryOutlined,
  PlayCircleOutlined,
  ReloadOutlined,
} from '@ant-design/icons-vue';

import type { UploadFile, UploadProps } from 'ant-design-vue';
import type { AudioIdentificationRecord } from '#/types/species';
import { AudioRecognitionApi } from '#/api/species';

// 响应式数据
const audioFileList = ref<UploadFile[]>([]);
const recognizing = ref(false);
const recognitionProgress = ref(0);
const recognitionStatus = ref<'active' | 'success' | 'exception'>('active');
const recognitionMessage = ref('');
const currentResult = ref<AudioIdentificationRecord | null>(null);

const historyLoading = ref(false);
const historyRecords = ref<AudioIdentificationRecord[]>([]);
const historyPage = ref(1);
const historyTotal = ref(0);

const audioPlayerVisible = ref(false);
const playingAudio = ref<AudioIdentificationRecord | null>(null);

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 上传前检查
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const isAudio = file.type?.startsWith('audio/');
  if (!isAudio) {
    message.error('只能上传音频文件！');
    return false;
  }

  const isLt50M = file.size! / 1024 / 1024 < 50;
  if (!isLt50M) {
    message.error('音频文件大小不能超过50MB！');
    return false;
  }

  return false; // 阻止自动上传
};

// 自定义上传和识别
const handleUpload: UploadProps['customRequest'] = async (options) => {
  const { file, onSuccess, onError } = options;
  
  try {
    recognizing.value = true;
    recognitionProgress.value = 0;
    recognitionStatus.value = 'active';
    recognitionMessage.value = '正在上传音频文件...';
    currentResult.value = null;

    // 模拟上传进度
    const uploadInterval = setInterval(() => {
      if (recognitionProgress.value < 30) {
        recognitionProgress.value += 5;
      }
    }, 200);

    setTimeout(() => {
      clearInterval(uploadInterval);
      recognitionProgress.value = 30;
      recognitionMessage.value = '正在进行音频识别...';
      
      // 模拟识别进度
      const recognitionInterval = setInterval(() => {
        if (recognitionProgress.value < 90) {
          recognitionProgress.value += 10;
        }
      }, 300);

      // 调用识别API
      AudioRecognitionApi.identifyAudio({ audioFile: file as File })
        .then(response => {
          clearInterval(recognitionInterval);
          
          if (response.success) {
            recognitionProgress.value = 100;
            recognitionStatus.value = 'success';
            recognitionMessage.value = '识别完成';
            currentResult.value = response.data;
            
            message.success('音频识别完成');
            onSuccess?.(response.data);
            
            // 刷新历史记录
            loadHistory();
          } else {
            throw new Error(response.message || '识别失败');
          }
        })
        .catch(error => {
          clearInterval(recognitionInterval);
          recognitionProgress.value = 100;
          recognitionStatus.value = 'exception';
          recognitionMessage.value = '识别失败';
          
          console.error('音频识别失败:', error);
          message.error('音频识别失败');
          onError?.(error);
        })
        .finally(() => {
          setTimeout(() => {
            recognizing.value = false;
            audioFileList.value = [];
          }, 2000);
        });
    }, 1000);

  } catch (error) {
    recognizing.value = false;
    console.error('上传失败:', error);
    message.error('上传失败');
    onError?.(error as Error);
  }
};

// 加载识别历史
const loadHistory = async () => {
  try {
    historyLoading.value = true;
    historyPage.value = 1;
    
    const response = await AudioRecognitionApi.getIdentificationHistory(1, 10);
    
    if (response.success) {
      historyRecords.value = response.data;
      historyTotal.value = response.pagination.total;
    } else {
      message.error(response.message || '加载历史记录失败');
    }
  } catch (error) {
    console.error('加载历史记录失败:', error);
    message.error('加载历史记录失败');
  } finally {
    historyLoading.value = false;
  }
};

// 加载更多历史记录
const loadMoreHistory = async () => {
  try {
    const nextPage = historyPage.value + 1;
    const response = await AudioRecognitionApi.getIdentificationHistory(nextPage, 10);
    
    if (response.success) {
      historyRecords.value.push(...response.data);
      historyPage.value = nextPage;
    } else {
      message.error(response.message || '加载更多记录失败');
    }
  } catch (error) {
    console.error('加载更多记录失败:', error);
    message.error('加载更多记录失败');
  }
};

// 播放音频
const handlePlayAudio = (record: AudioIdentificationRecord) => {
  playingAudio.value = record;
  audioPlayerVisible.value = true;
};

// 删除识别记录
const handleDeleteRecord = async (record: AudioIdentificationRecord) => {
  try {
    const response = await AudioRecognitionApi.deleteIdentificationRecord(record.id);
    if (response.success) {
      message.success('记录删除成功');
      loadHistory();
    } else {
      message.error(response.message || '删除失败');
    }
  } catch (error) {
    console.error('删除记录失败:', error);
    message.error('删除记录失败');
  }
};

// 组件挂载时加载历史记录
onMounted(() => {
  loadHistory();
});
</script>

<style scoped>
.audio-recognition-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}
</style>
