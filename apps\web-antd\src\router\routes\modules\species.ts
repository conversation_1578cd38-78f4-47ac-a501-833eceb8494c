/**
 * 物种管理系统路由配置
 */

import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:leaf',
      title: '物种管理',
      order: 1000,
    },
    name: 'Species',
    path: '/species',
    component: () => import('#/layouts').then((m) => m.BasicLayout),
    children: [
      {
        name: 'SpeciesList',
        path: '/species/list',
        component: () => import('#/views/species-management/list/index.vue'),
        meta: {
          icon: 'lucide:list',
          title: '物种列表',
        },
      },
      {
        name: 'SpeciesCreate',
        path: '/species/create',
        component: () => import('#/views/species-management/editor/index.vue'),
        meta: {
          icon: 'lucide:plus',
          title: '新增物种',
          hideInMenu: true,
        },
      },
      {
        name: 'SpeciesEdit',
        path: '/species/edit/:id',
        component: () => import('#/views/species-management/editor/index.vue'),
        meta: {
          icon: 'lucide:edit',
          title: '编辑物种',
          hideInMenu: true,
          activePath: '/species/list',
        },
      },
      {
        name: 'SpeciesMedia',
        path: '/species/:id/media',
        component: () => import('#/views/species-management/media/index.vue'),
        meta: {
          icon: 'lucide:image',
          title: '媒体管理',
          hideInMenu: true,
          activePath: '/species/list',
        },
      },
      {
        name: 'AudioRecognition',
        path: '/species/audio-recognition',
        component: () => import('#/views/species-management/audio-recognition/index.vue'),
        meta: {
          icon: 'lucide:mic',
          title: '音频识别',
        },
      },
    ],
  },
];

export default routes;
