/**
 * 物种管理系统核心数据类型定义
 */

// 保护状况枚举（基于IUCN红色名录）
export enum ConservationStatus {
  LC = 'LC', // 无危 (Least Concern)
  NT = 'NT', // 近危 (Near Threatened)
  VU = 'VU', // 易危 (Vulnerable)
  EN = 'EN', // 濒危 (Endangered)
  CR = 'CR', // 极危 (Critically Endangered)
  EW = 'EW', // 野外灭绝 (Extinct in the Wild)
  EX = 'EX', // 灭绝 (Extinct)
  DD = 'DD', // 数据缺乏 (Data Deficient)
  NE = 'NE', // 未评估 (Not Evaluated)
}

// 媒体文件类型
export enum MediaType {
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
}

// 物种分类信息
export interface SpeciesTaxonomy {
  kingdom: string;    // 界
  phylum: string;     // 门
  class: string;      // 纲
  order: string;      // 目
  family: string;     // 科
  genus: string;      // 属
  species: string;    // 种
}

// 地理坐标
export interface GeoCoordinate {
  latitude: number;   // 纬度
  longitude: number;  // 经度
  altitude?: number;  // 海拔（可选）
}

// 边界框
export interface BoundingBox {
  north: number;
  south: number;
  east: number;
  west: number;
}

// 音频元数据
export interface AudioMetadata {
  duration: number;              // 时长（秒）
  sampleRate: number;           // 采样率
  bitDepth: number;             // 位深度
  channels: number;             // 声道数
  recordingLocation?: string;    // 录音地点
  recordingTime?: string;       // 录音时间
  behaviorDescription?: string;  // 行为描述
  sensitivity?: string;         // 灵敏度
  amplification?: string;       // 放大倍数
  spectrogramUrls?: {           // 频谱图URL
    hologram?: string;          // 全息谱
    spectrum?: string;          // 频谱
  };
}

// 媒体文件基础信息
export interface MediaFile {
  id: string;
  speciesId: string;
  type: MediaType;
  filename: string;
  originalName: string;
  url: string;
  thumbnailUrl?: string;        // 缩略图URL
  size: number;                 // 文件大小（字节）
  mimeType: string;
  title?: string;               // 标题
  description?: string;         // 描述
  metadata?: AudioMetadata;     // 音频元数据（仅音频文件）
  coordinates?: GeoCoordinate;  // 地理坐标（主要用于音频采集点）
  createdAt: string;
  updatedAt: string;
}

// 地理分布数据
export interface GeographicDistribution {
  id: string;
  speciesId: string;
  kmlFileUrl: string;
  kmlFileName: string;
  coordinates: GeoCoordinate[];
  boundingBox: BoundingBox;
  createdAt: string;
  updatedAt: string;
}

// 物种基础信息
export interface Species {
  id: string;
  chineseName: string;          // 中文名
  englishName?: string;         // 英文名
  latinName: string;            // 拉丁学名
  taxonomy: SpeciesTaxonomy;    // 分类信息
  conservationStatus: ConservationStatus; // 保护状况
  description?: string;         // 详细描述（富文本）
  representativeImageId?: string; // 代表性图片ID
  mediaFiles?: MediaFile[];     // 媒体文件列表
  geographicDistribution?: GeographicDistribution; // 地理分布
  createdAt: string;
  updatedAt: string;
}

// 音频识别结果
export interface IdentifiedSpecies {
  speciesId: string;
  species: Species;
  confidence: number;           // 置信度 (0-1)
  probability: number;          // 概率
}

// 音频识别记录
export interface AudioIdentificationRecord {
  id: string;
  audioFileId: string;
  audioFile: MediaFile;
  identifiedSpecies: IdentifiedSpecies[];
  processingTime: number;       // 处理时间（毫秒）
  rawResult: any;              // 原始识别结果
  createdAt: string;
}

// API响应基础结构
export interface ApiResponse<T = any> {
  success: boolean;
  data: T | null;
  message?: string;
  code?: string;
}

// 分页信息
export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

// 分页响应
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: PaginationInfo;
}

// 物种列表查询参数
export interface SpeciesListQuery {
  page?: number;
  limit?: number;
  search?: string;              // 搜索关键词
  category?: string;            // 分类筛选
  conservationStatus?: ConservationStatus; // 保护状况筛选
  sortBy?: 'name' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

// 物种创建/更新请求
export interface SpeciesCreateRequest {
  chineseName: string;
  englishName?: string;
  latinName: string;
  taxonomy: SpeciesTaxonomy;
  conservationStatus: ConservationStatus;
  description?: string;
  representativeImageId?: string;
}

export interface SpeciesUpdateRequest extends Partial<SpeciesCreateRequest> {
  id: string;
}

// 媒体文件上传请求
export interface MediaUploadRequest {
  speciesId: string;
  type: MediaType;
  files: File[];
}

// 媒体元数据更新请求
export interface MediaMetadataUpdateRequest {
  id: string;
  title?: string;
  description?: string;
  coordinates?: GeoCoordinate;
  metadata?: Partial<AudioMetadata>;
}

// KML文件上传请求
export interface KMLUploadRequest {
  speciesId: string;
  file: File;
}

// 音频识别请求
export interface AudioIdentifyRequest {
  audioFile: File;
}

// 表单验证规则类型
export interface FormValidationRule {
  required?: boolean;
  message?: string;
  pattern?: RegExp;
  min?: number;
  max?: number;
  validator?: (rule: any, value: any) => Promise<void>;
}

// 表单字段配置
export interface FormFieldConfig {
  label: string;
  key: string;
  type: 'input' | 'textarea' | 'select' | 'upload' | 'coordinate';
  rules?: FormValidationRule[];
  options?: { label: string; value: any }[];
  placeholder?: string;
  disabled?: boolean;
}
