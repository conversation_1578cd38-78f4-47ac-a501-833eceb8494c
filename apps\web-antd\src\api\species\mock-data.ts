/**
 * 物种管理系统Mock数据
 */

import type {
  AudioIdentificationRecord,
  ConservationStatus,
  GeographicDistribution,
  MediaFile,
  MediaType,
  Species,
} from '#/types/species';

// Mock物种数据
export const mockSpeciesData: Species[] = [
  {
    id: 'species_001',
    chineseName: '东北虎',
    englishName: 'Siberian Tiger',
    latinName: 'Panthera tigris altaica',
    taxonomy: {
      kingdom: '动物界',
      phylum: '脊索动物门',
      class: '哺乳纲',
      order: '食肉目',
      family: '猫科',
      genus: '豹属',
      species: '虎',
    },
    conservationStatus: 'EN' as ConservationStatus,
    description: `
      <h2>形态特征</h2>
      <p>东北虎是现存体型最大的猫科动物，雄性体长可达3.3米，体重达到320公斤。毛色橙黄，黑色条纹宽而稀疏，间距较宽。</p>
      
      <h2>生活习性</h2>
      <p>东北虎是独居动物，领域性很强。主要在夜间活动，白天多在洞穴中休息。善于游泳，能游渡宽阔的河流。</p>
      
      <h2>栖息环境</h2>
      <p>主要栖息在森林、灌木丛和野草丛生的地区。在中国主要分布在黑龙江、吉林省的山区。</p>
    `,
    representativeImageId: 'media_001',
    createdAt: '2024-01-15T08:30:00Z',
    updatedAt: '2024-01-20T14:22:00Z',
  },
  {
    id: 'species_002',
    chineseName: '大熊猫',
    englishName: 'Giant Panda',
    latinName: 'Ailuropoda melanoleuca',
    taxonomy: {
      kingdom: '动物界',
      phylum: '脊索动物门',
      class: '哺乳纲',
      order: '食肉目',
      family: '熊科',
      genus: '大熊猫属',
      species: '大熊猫',
    },
    conservationStatus: 'VU' as ConservationStatus,
    description: `
      <h2>形态特征</h2>
      <p>大熊猫体型肥硕似熊，丰腴富态，头圆尾短。头部和身体毛色黑白相间分明，但黑非纯黑，白也不是纯白，而是黑中透褐，白中带黄。</p>
      
      <h2>生活习性</h2>
      <p>大熊猫每天除去一半进食的时间，剩下的一半时间多数便是在睡梦中度过。在野外，大熊猫在每两次进食的中间睡2-4个小时。</p>
      
      <h2>食性特点</h2>
      <p>大熊猫99%的食物都是竹子，可食用的竹类植物共有12属、60多种。野外大熊猫的寿命为18-20岁，圈养状态下可以超过30岁。</p>
    `,
    representativeImageId: 'media_002',
    createdAt: '2024-01-10T10:15:00Z',
    updatedAt: '2024-01-18T16:45:00Z',
  },
  {
    id: 'species_003',
    chineseName: '金丝猴',
    englishName: 'Golden Snub-nosed Monkey',
    latinName: 'Rhinopithecus roxellana',
    taxonomy: {
      kingdom: '动物界',
      phylum: '脊索动物门',
      class: '哺乳纲',
      order: '灵长目',
      family: '猴科',
      genus: '仰鼻猴属',
      species: '川金丝猴',
    },
    conservationStatus: 'EN' as ConservationStatus,
    description: `
      <h2>形态特征</h2>
      <p>金丝猴毛质柔软，鼻子上翘，有缅甸金丝猴、川金丝猴、滇金丝猴、黔金丝猴、越南金丝猴5种，其中除缅甸金丝猴和越南金丝猴外，均为中国特有的珍贵动物。</p>
      
      <h2>生活习性</h2>
      <p>金丝猴群栖高山密林中，以浆果、竹笋、苔藓为食，亦喜食鸟蛋。栖息地海拔很高，身上的长毛可耐寒。</p>
    `,
    representativeImageId: 'media_003',
    createdAt: '2024-01-12T14:20:00Z',
    updatedAt: '2024-01-19T11:30:00Z',
  },
  {
    id: 'species_004',
    chineseName: '朱鹮',
    englishName: 'Crested Ibis',
    latinName: 'Nipponia nippon',
    taxonomy: {
      kingdom: '动物界',
      phylum: '脊索动物门',
      class: '鸟纲',
      order: '鹈形目',
      family: '鹮科',
      genus: '朱鹮属',
      species: '朱鹮',
    },
    conservationStatus: 'EN' as ConservationStatus,
    description: `
      <h2>形态特征</h2>
      <p>朱鹮是稀世珍禽，体长约60厘米，体重约1.8公斤。雌雄同色，成鸟全身羽毛白色，但羽基微染粉红色。</p>
      
      <h2>生活习性</h2>
      <p>朱鹮栖息于海拔1200-1400米的疏林地带，在附近的溪流、沼泽及稻田内觅食，以鱼类、蛙类、小型无脊椎动物等为食。</p>
    `,
    representativeImageId: 'media_004',
    createdAt: '2024-01-08T09:45:00Z',
    updatedAt: '2024-01-16T13:15:00Z',
  },
  {
    id: 'species_005',
    chineseName: '藏羚羊',
    englishName: 'Tibetan Antelope',
    latinName: 'Pantholops hodgsonii',
    taxonomy: {
      kingdom: '动物界',
      phylum: '脊索动物门',
      class: '哺乳纲',
      order: '偶蹄目',
      family: '牛科',
      genus: '藏羚羊属',
      species: '藏羚羊',
    },
    conservationStatus: 'NT' as ConservationStatus,
    description: `
      <h2>形态特征</h2>
      <p>藏羚羊体形与黄羊相似，体长为100-140厘米，肩高75-91厘米，体重45-60千克。头形宽长，吻部粗壮，鼻部稍隆起。</p>
      
      <h2>生活习性</h2>
      <p>藏羚羊栖息于海拔3700-5500米的高山草原、草甸和高寒荒漠地带，早晚觅食，善奔跑。</p>
    `,
    representativeImageId: 'media_005',
    createdAt: '2024-01-05T16:30:00Z',
    updatedAt: '2024-01-14T10:20:00Z',
  },
];

// Mock媒体文件数据
export const mockMediaFiles: MediaFile[] = [
  // 东北虎的媒体文件
  {
    id: 'media_001',
    speciesId: 'species_001',
    type: 'image' as MediaType,
    filename: 'siberian_tiger_001.jpg',
    originalName: '东北虎-栖息地.jpg',
    url: 'https://images.unsplash.com/photo-1561731216-c3a4d99437d5?w=800',
    thumbnailUrl: 'https://images.unsplash.com/photo-1561731216-c3a4d99437d5?w=200',
    size: 2048576,
    mimeType: 'image/jpeg',
    title: '东北虎栖息地环境',
    description: '东北虎在自然栖息地中的生活场景',
    createdAt: '2024-01-15T08:30:00Z',
    updatedAt: '2024-01-15T08:30:00Z',
  },
  {
    id: 'media_006',
    speciesId: 'species_001',
    type: 'audio' as MediaType,
    filename: 'tiger_roar_001.mp3',
    originalName: '东北虎咆哮声.mp3',
    url: '/mock-audio/tiger-roar.mp3',
    size: 1024000,
    mimeType: 'audio/mpeg',
    title: '东北虎咆哮声',
    description: '成年雄性东北虎的威慑性咆哮',
    coordinates: {
      latitude: 44.5588,
      longitude: 129.6917,
    },
    metadata: {
      duration: 15.5,
      sampleRate: 44100,
      bitDepth: 16,
      channels: 2,
      recordingLocation: '黑龙江省牡丹江市',
      recordingTime: '2024-01-10T06:30:00Z',
      behaviorDescription: '领域宣示行为',
      sensitivity: '高',
      amplification: '2x',
      spectrogramUrls: {
        hologram: '/mock-images/tiger-hologram.png',
        spectrum: '/mock-images/tiger-spectrum.png',
      },
    },
    createdAt: '2024-01-15T09:15:00Z',
    updatedAt: '2024-01-15T09:15:00Z',
  },
  // 大熊猫的媒体文件
  {
    id: 'media_002',
    speciesId: 'species_002',
    type: 'image' as MediaType,
    filename: 'giant_panda_001.jpg',
    originalName: '大熊猫-进食.jpg',
    url: 'https://images.unsplash.com/photo-1564349683136-77e08dba1ef7?w=800',
    thumbnailUrl: 'https://images.unsplash.com/photo-1564349683136-77e08dba1ef7?w=200',
    size: 1856432,
    mimeType: 'image/jpeg',
    title: '大熊猫进食竹子',
    description: '大熊猫在自然环境中进食竹子的场景',
    createdAt: '2024-01-10T10:15:00Z',
    updatedAt: '2024-01-10T10:15:00Z',
  },
  {
    id: 'media_007',
    speciesId: 'species_002',
    type: 'video' as MediaType,
    filename: 'panda_playing_001.mp4',
    originalName: '大熊猫玩耍视频.mp4',
    url: '/mock-video/panda-playing.mp4',
    thumbnailUrl: '/mock-images/panda-video-thumb.jpg',
    size: 15728640,
    mimeType: 'video/mp4',
    title: '大熊猫幼崽玩耍',
    description: '两只大熊猫幼崽在竹林中嬉戏玩耍的珍贵视频',
    createdAt: '2024-01-10T11:20:00Z',
    updatedAt: '2024-01-10T11:20:00Z',
  },
  // 金丝猴的媒体文件
  {
    id: 'media_003',
    speciesId: 'species_003',
    type: 'image' as MediaType,
    filename: 'golden_monkey_001.jpg',
    originalName: '金丝猴-群体.jpg',
    url: 'https://images.unsplash.com/photo-1540573133985-87b6da6d54a9?w=800',
    thumbnailUrl: 'https://images.unsplash.com/photo-1540573133985-87b6da6d54a9?w=200',
    size: 2234567,
    mimeType: 'image/jpeg',
    title: '金丝猴群体活动',
    description: '金丝猴家族在高山森林中的群体活动',
    createdAt: '2024-01-12T14:20:00Z',
    updatedAt: '2024-01-12T14:20:00Z',
  },
  // 朱鹮的媒体文件
  {
    id: 'media_004',
    speciesId: 'species_004',
    type: 'image' as MediaType,
    filename: 'crested_ibis_001.jpg',
    originalName: '朱鹮-飞翔.jpg',
    url: 'https://images.unsplash.com/photo-1444464666168-49d633b86797?w=800',
    thumbnailUrl: 'https://images.unsplash.com/photo-1444464666168-49d633b86797?w=200',
    size: 1923456,
    mimeType: 'image/jpeg',
    title: '朱鹮飞翔姿态',
    description: '朱鹮在湿地上空优雅飞翔的瞬间',
    createdAt: '2024-01-08T09:45:00Z',
    updatedAt: '2024-01-08T09:45:00Z',
  },
  {
    id: 'media_008',
    speciesId: 'species_004',
    type: 'audio' as MediaType,
    filename: 'ibis_call_001.wav',
    originalName: '朱鹮鸣叫声.wav',
    url: '/mock-audio/ibis-call.wav',
    size: 2048000,
    mimeType: 'audio/wav',
    title: '朱鹮求偶鸣叫',
    description: '朱鹮在繁殖期的求偶鸣叫声',
    coordinates: {
      latitude: 33.3706,
      longitude: 107.2734,
    },
    metadata: {
      duration: 8.2,
      sampleRate: 48000,
      bitDepth: 24,
      channels: 1,
      recordingLocation: '陕西省汉中市洋县',
      recordingTime: '2024-01-05T07:15:00Z',
      behaviorDescription: '求偶鸣叫',
      sensitivity: '中',
      amplification: '1.5x',
    },
    createdAt: '2024-01-08T10:30:00Z',
    updatedAt: '2024-01-08T10:30:00Z',
  },
  // 藏羚羊的媒体文件
  {
    id: 'media_005',
    speciesId: 'species_005',
    type: 'image' as MediaType,
    filename: 'tibetan_antelope_001.jpg',
    originalName: '藏羚羊-迁徙.jpg',
    url: 'https://images.unsplash.com/photo-1549366021-9f761d040a94?w=800',
    thumbnailUrl: 'https://images.unsplash.com/photo-1549366021-9f761d040a94?w=200',
    size: 2567890,
    mimeType: 'image/jpeg',
    title: '藏羚羊迁徙队伍',
    description: '藏羚羊群体在青藏高原进行季节性迁徙',
    createdAt: '2024-01-05T16:30:00Z',
    updatedAt: '2024-01-05T16:30:00Z',
  },
];

// Mock音频识别记录数据
export const mockIdentificationRecords: AudioIdentificationRecord[] = [
  {
    id: 'record_001',
    audioFileId: 'media_006',
    audioFile: mockMediaFiles.find(m => m.id === 'media_006')!,
    identifiedSpecies: [
      {
        speciesId: 'species_001',
        species: mockSpeciesData.find(s => s.id === 'species_001')!,
        confidence: 0.95,
        probability: 0.89,
      },
    ],
    processingTime: 3500,
    rawResult: {
      model_version: '2.1.0',
      processing_timestamp: '2024-01-15T09:20:00Z',
      audio_features: {
        dominant_frequency: 180,
        duration: 15.5,
        amplitude_peak: 0.87,
      },
    },
    createdAt: '2024-01-15T09:20:00Z',
  },
  {
    id: 'record_002',
    audioFileId: 'media_008',
    audioFile: mockMediaFiles.find(m => m.id === 'media_008')!,
    identifiedSpecies: [
      {
        speciesId: 'species_004',
        species: mockSpeciesData.find(s => s.id === 'species_004')!,
        confidence: 0.87,
        probability: 0.82,
      },
    ],
    processingTime: 2800,
    rawResult: {
      model_version: '2.1.0',
      processing_timestamp: '2024-01-08T10:35:00Z',
      audio_features: {
        dominant_frequency: 2400,
        duration: 8.2,
        amplitude_peak: 0.65,
      },
    },
    createdAt: '2024-01-08T10:35:00Z',
  },
];

// Mock地理分布数据
export const mockGeographicDistributions: GeographicDistribution[] = [
  {
    id: 'geo_001',
    speciesId: 'species_001',
    kmlFileUrl: '/mock-kml/siberian-tiger-distribution.kml',
    kmlFileName: '东北虎分布区域.kml',
    coordinates: [
      { latitude: 44.5588, longitude: 129.6917 },
      { latitude: 45.7536, longitude: 126.6417 },
      { latitude: 42.9027, longitude: 131.8719 },
    ],
    boundingBox: {
      north: 45.7536,
      south: 42.9027,
      east: 131.8719,
      west: 126.6417,
    },
    createdAt: '2024-01-15T08:30:00Z',
    updatedAt: '2024-01-15T08:30:00Z',
  },
  {
    id: 'geo_002',
    speciesId: 'species_002',
    kmlFileUrl: '/mock-kml/giant-panda-distribution.kml',
    kmlFileName: '大熊猫分布区域.kml',
    coordinates: [
      { latitude: 31.2304, longitude: 103.8370 },
      { latitude: 33.5986, longitude: 104.0618 },
      { latitude: 32.0617, longitude: 106.5516 },
    ],
    boundingBox: {
      north: 33.5986,
      south: 31.2304,
      east: 106.5516,
      west: 103.8370,
    },
    createdAt: '2024-01-10T10:15:00Z',
    updatedAt: '2024-01-10T10:15:00Z',
  },
];
