# 需求文档

## 介绍

物种详情管理系统是一个为后台管理员设计的全面管理界面，用于创建、编辑和管理物种详情页面上展示的所有数据元素。该系统将支持物种基础信息、详细介绍、媒体资源、地理分布数据和音频识别等核心功能的管理。

## 需求

### 需求 1 - 物种基础信息管理

**用户故事：** 作为后台管理员，我希望能够管理物种的基础信息，以便在物种详情页的信息框中正确展示核心数据。

#### 验收标准

1. WHEN 管理员访问物种基础信息管理页面 THEN 系统 SHALL 显示物种名称输入字段（中文名、英文名、拉丁学名）
2. WHEN 管理员输入物种分类信息 THEN 系统 SHALL 提供界、门、纲、目、科、属、种的独立输入字段或下拉菜单
3. WHEN 管理员设置保护状况 THEN 系统 SHALL 提供基于IUCN红色名录等级的下拉选择菜单（无危、近危、易危等）
4. WHEN 管理员选择代表性图片 THEN 系统 SHALL 允许从已上传图集中指定一张作为代表性照片
5. WHEN 管理员保存基础信息 THEN 系统 SHALL 验证必填字段并保存数据

### 需求 2 - 物种详细介绍管理

**用户故事：** 作为后台管理员，我希望能够编辑物种的详细描述内容，以便在详情页主内容区提供丰富的文字信息。

#### 验收标准

1. WHEN 管理员访问详细介绍编辑页面 THEN 系统 SHALL 提供富文本编辑器界面
2. WHEN 管理员使用富文本编辑器 THEN 系统 SHALL 支持段落、加粗、斜体、列表等格式化功能
3. WHEN 管理员编辑物种描述 THEN 系统 SHALL 支持形态、习性、分布等内容的结构化编辑
4. WHEN 管理员保存描述内容 THEN 系统 SHALL 保存格式化的文本内容
5. WHEN 管理员预览内容 THEN 系统 SHALL 提供所见即所得的预览功能

### 需求 3 - 媒体资源库管理

**用户故事：** 作为后台管理员，我希望能够管理每个物种的媒体资源，以便为物种详情页提供丰富的视觉和听觉内容。

#### 验收标准

1. WHEN 管理员访问图集管理 THEN 系统 SHALL 支持批量上传多张高清图片
2. WHEN 图片上传完成 THEN 系统 SHALL 以列表或缩略图形式展示所有已上传图片
3. WHEN 管理员管理图片 THEN 系统 SHALL 允许对单张图片进行删除或替换操作
4. WHEN 管理员上传视频 THEN 系统 SHALL 支持视频文件上传并允许添加标题或描述
5. WHEN 管理员管理视频 THEN 系统 SHALL 支持视频的删除和排序功能
6. WHEN 管理员上传音频 THEN 系统 SHALL 支持WAV、MP3格式的音频文件上传
7. WHEN 管理员编辑音频元数据 THEN 系统 SHALL 允许添加录音地点、录音时间、行为描述、采样位数、灵敏度、放大倍数等信息
8. WHEN 管理员管理音频 THEN 系统 SHALL 支持音频文件的删除和排序
9. WHEN 管理员处理音频 THEN 系统 SHALL 支持通过单选框形式预生成全息谱和频谱图片

### 需求 4 - 地理分布数据管理

**用户故事：** 作为后台管理员，我希望能够管理物种的地理分布数据，以便在详情页地图上准确展示物种分布范围。

#### 验收标准

1. WHEN 管理员上传KML文件 THEN 系统 SHALL 提供文件上传入口并解析坐标数据
2. WHEN KML文件解析完成 THEN 系统 SHALL 在物种分布范围图中展示解析出的坐标
3. WHEN 管理员管理KML文件 THEN 系统 SHALL 支持对已上传KML文件的替换或删除操作
4. WHEN KML文件格式错误 THEN 系统 SHALL 提供清晰的错误提示信息

### 需求 5 - 采集点数据管理

**用户故事：** 作为后台管理员，我希望能够管理音频文件的地理坐标信息，以便支持声音数据采集点图功能。

#### 验收标准

1. WHEN 管理员上传或编辑音频文件 THEN 系统 SHALL 提供地理坐标附加功能
2. WHEN 管理员手动输入坐标 THEN 系统 SHALL 允许输入精确的经度和纬度信息
3. WHEN 坐标信息保存 THEN 系统 SHALL 验证坐标格式的有效性
4. WHEN 查看采集点 THEN 系统 SHALL 在地图上显示与音频文件关联的采集点位置

### 需求 6 - 动物音频识别

**用户故事：** 作为后台管理员，我希望能够使用音频识别功能，以便通过音频文件自动识别物种信息。

#### 验收标准

1. WHEN 管理员上传音频进行识别 THEN 系统 SHALL 支持WAV、MP3格式文件上传
2. WHEN 音频上传完成 THEN 系统 SHALL 调用甲方提供的识别模型接口
3. WHEN 识别完成 THEN 系统 SHALL 展示识别结果中的对应物种信息
4. WHEN 查看识别记录 THEN 系统 SHALL 提供上传音频文件的播放功能
5. WHEN 管理识别记录 THEN 系统 SHALL 以列表形式展示识别数据（具体字段根据返回数据确定）
6. IF 识别失败 THEN 系统 SHALL 提供清晰的错误信息和重试选项

### 需求 7 - 系统通用功能

**用户故事：** 作为后台管理员，我希望系统提供稳定可靠的基础功能，以便高效地完成物种数据管理工作。

#### 验收标准

1. WHEN 管理员登录系统 THEN 系统 SHALL 验证用户权限并提供安全的访问控制
2. WHEN 系统处理大文件上传 THEN 系统 SHALL 提供上传进度显示和错误处理
3. WHEN 数据操作失败 THEN 系统 SHALL 提供清晰的错误提示和恢复建议
4. WHEN 管理员执行批量操作 THEN 系统 SHALL 提供操作确认和撤销功能
5. WHEN 系统响应时间超过预期 THEN 系统 SHALL 提供加载状态指示器
